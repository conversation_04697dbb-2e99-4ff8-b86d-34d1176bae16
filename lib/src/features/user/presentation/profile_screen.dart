// Update Name
// Update Email Address
// Update Password

// Haptic Feedback

// Social - Instagram
// Social - Twitter

// Terms of Service
// Privacy Policy

// Logout
// Delete Account

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:rive/rive.dart' hide image;
import 'package:vocadex/src/features/notifications/screens/notification_debug_screen.dart';

// lib/src/features/user/presentation/profile_screen.dart

import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/subscriptions/presentation/show_paywall.dart';

import 'package:vocadex/src/features/auth/model/auth_state.dart';
import 'package:vocadex/src/features/auth/model/auth_user.dart';
import 'package:vocadex/src/features/auth/presentation/widgets/auth_button.dart';
import 'package:vocadex/src/features/auth/providers/auth_controller.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/features/auth/service/firebase_auth_service.dart';
import 'package:vocadex/src/features/user/models/user_model.dart';
import 'package:vocadex/src/features/user/providers/user_notification_preferences_provider.dart';
import 'package:vocadex/src/features/user/presentation/user_notification_history_screen.dart';
import 'package:vocadex/src/services/firebase_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';
import 'package:vocadex/src/features/subscriptions/providers/subscription_notifier.dart';
import 'package:vocadex/src/features/feedback/widgets/feedback_dialog.dart';
import 'package:vocadex/src/features/localization/app_strings.dart';
import 'package:vocadex/src/features/notifications/providers/admin_access_provider.dart';
import 'package:vocadex/src/features/notifications/presentation/admin_panel_screen.dart';

/// A FutureProvider that fetches the current user's data
final userProfileDataProvider = FutureProvider<UserModel?>((ref) async {
  final firebaseService = FirebaseService();
  try {
    final authState = ref.watch(authStateNotifierProvider);

    return authState.maybeWhen(
      authenticated: (_) async {
        return await firebaseService.getUserData();
      },
      orElse: () => null,
    );
  } catch (e) {
    debugPrint('Error fetching user data: $e');
    return null;
  }
});

/// Provider to check if user is anonymous
final isAnonymousProvider = FutureProvider<bool>((ref) async {
  // Watch the auth state to ensure this provider updates when auth changes
  ref.watch(authStateNotifierProvider);

  final authService = ref.read(firebaseAuthServiceProvider);
  try {
    final isAnonymous = await authService.isAnonymousUser();
    debugPrint('Anonymous status check: $isAnonymous');
    return isAnonymous;
  } catch (e) {
    debugPrint('Error checking anonymous status: $e');
    return false;
  }
});

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateNotifierProvider);
    final userDataAsync = ref.watch(userProfileDataProvider);
    final isPremium = ref.watch(subscriptionStateProvider);
    final isAnonymousAsync = ref.watch(isAnonymousProvider);

    // Add a listener to refresh user data when auth state changes
    ref.listen(authStateNotifierProvider, (previous, current) {
      if (previous != current) {
        ref.invalidate(userProfileDataProvider);
        ref.invalidate(isAnonymousProvider); // Also invalidate anonymous status
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              // Show help dialog
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Need Help?'),
                  content:
                      const Text('Contact <NAME_EMAIL>'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Close'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User profile header
            authState.when(
              initial: () => const _LoadingProfileHeader(),
              loading: () => const _LoadingProfileHeader(),
              authenticated: (user) => _buildProfileHeader(
                  context, user, userDataAsync, isPremium, isAnonymousAsync),
              unauthenticated: () => const _GuestProfileHeader(),
              error: (message) => _ErrorProfileHeader(message: message),
            ),

            const SizedBox(height: 24),

            // Account Settings Section
            const _SectionHeader(title: 'Account Settings'),

            // Profile Settings
            _buildProfileSettingsSection(context, authState, isAnonymousAsync),

            const SizedBox(height: 16),

            // App Preferences Section
            // const _SectionHeader(title: 'App Preferences'),
            // _buildPreferencesSection(context),

            const SizedBox(height: 16),

            // Notification Settings Section
            _buildNotificationSettingsSection(context, ref),

            const SizedBox(height: 16),

            // Admin Panel Section (only visible to admin users)
            _buildAdminPanelSection(context, ref),

            const SizedBox(height: 16),

            // Support Section
            const _SectionHeader(title: 'Support'),
            _buildSupportSection(context),

            const SizedBox(height: 16),

            // Legal and About Section
            const _SectionHeader(title: 'Legal & Info'),
            _buildLegalSection(context),

            const SizedBox(height: 24),

            // Sign Out & Delete Account Buttons
            _buildAccountActions(context, ref, authState, isAnonymousAsync),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(
    BuildContext context,
    AuthUser user,
    AsyncValue<UserModel?> userDataAsync,
    bool isPremium,
    AsyncValue<bool> isAnonymousAsync,
  ) {
    return userDataAsync.when(
      data: (userData) {
        final String displayName = userData?.firstName ?? 'User';
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 36,
                      backgroundColor: AppColors.getPrimaryColor(
                              Theme.of(context).brightness)
                          .withAlpha(51),
                      child: userData?.imageUrl != null &&
                              userData!.imageUrl!.isNotEmpty
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(36),
                              child: Image.network(
                                userData.imageUrl!,
                                width: 72,
                                height: 72,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    Icon(
                                  Icons.person,
                                  size: 36,
                                  color: AppColors.getPrimaryColor(
                                      Theme.of(context).brightness),
                                ),
                              ),
                            )
                          : Icon(
                              Icons.person,
                              size: 36,
                              color: AppColors.getPrimaryColor(
                                  Theme.of(context).brightness),
                            ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            displayName,
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          Text(
                            user.email,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: AppColors.grey,
                                ),
                          ),
                          const SizedBox(height: 8),
                          isAnonymousAsync.when(
                            data: (isAnonymous) => isAnonymous
                                ? _buildConvertAccountButton(context)
                                : const SizedBox.shrink(),
                            loading: () => const SizedBox.shrink(),
                            error: (_, __) => const SizedBox.shrink(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const Divider(height: 24),
                // Premium subscription status
                isPremium
                    ? _buildPremiumBadge(context)
                    : _buildSubscriptionCard(context),
              ],
            ),
          ),
        );
      },
      loading: () => const _LoadingProfileHeader(),
      error: (error, stack) => _ErrorProfileHeader(message: error.toString()),
    );
  }

  Widget _buildConvertAccountButton(BuildContext context) {
    return OutlinedButton(
      onPressed: () {
        // Navigate to create full account screen or show dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Create Full Account'),
            content: const Text(
              'Would you like to convert your guest account to a permanent account? '
              'This will allow you to access your vocabulary from any device.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Later'),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.pop(context);
                  context.pushNamed(RouteNames.signup);
                },
                child: const Text('Create Account'),
              ),
            ],
          ),
        );
      },
      style: OutlinedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        side: BorderSide(color: Theme.of(context).primaryColor),
      ),
      child: const Text('Convert Guest Account'),
    );
  }

  Widget _buildPremiumBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [AppColors.gradientButton1, AppColors.gradientButton2],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.star,
            color: AppColors.white,
            size: 24,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Premium Subscriber',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Text(
                  'Enjoy unlimited vocabulary cards!',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.white.withAlpha(204),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionCard(BuildContext context) {
    return Card(
      elevation: 0,
      color: AppColors.getBackgroundColor(Theme.of(context).brightness),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: AppColors.getPrimaryColor(Theme.of(context).brightness)
              .withAlpha(76),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.workspace_premium,
                  color:
                      AppColors.getPrimaryColor(Theme.of(context).brightness),
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Upgrade to Premium',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.getTextColor(
                                      Theme.of(context).brightness),
                                ),
                      ),
                      Text(
                        'Unlock unlimited vocabulary cards, AI-powered suggestions, and more!',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.getTextColor(
                                  Theme.of(context).brightness)
                              .withAlpha(204),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  gradient: const LinearGradient(
                    colors: [
                      AppColors.gradientButton1,
                      AppColors.gradientButton2
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
                child: ElevatedButton(
                  onPressed: () {
                    // Show paywall
                    final showPaywall = ShowPaywall();
                    showPaywall.presentPaywall();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: AppColors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Subscribe Now'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSettingsSection(
    BuildContext context,
    AuthState authState,
    AsyncValue<bool> isAnonymousAsync,
  ) {
    return isAnonymousAsync.when(
      data: (isAnonymous) {
        if (isAnonymous) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Create a full account to access profile settings.',
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              _SettingsItem(
                icon: Icons.person_outline,
                title: 'Update Profile',
                subtitle: 'Change your name and profile picture',
                onTap: () => _showUpdateProfileDialog(context),
              ),
              const Divider(height: 1),
              _SettingsItem(
                icon: Icons.alternate_email,
                title: 'Change Email Address',
                subtitle: 'Update your email address',
                onTap: () => _showUpdateEmailDialog(context),
              ),
              const Divider(height: 1),
              _SettingsItem(
                icon: Icons.lock_outline,
                title: 'Change Password',
                subtitle: 'Update your password',
                onTap: () => _showChangePasswordDialog(context),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'Error loading profile settings',
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildLegalSection(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _SettingsItem(
            icon: Icons.description_outlined,
            title: 'Terms of Service',
            subtitle: 'Read our terms of service',
            onTap: () => _launchURL('https://tabemedia.co.uk/terms'),
          ),
          const Divider(height: 1),
          _SettingsItem(
            icon: Icons.security_outlined,
            title: 'Privacy Policy',
            subtitle: 'Read our privacy policy',
            onTap: () => _launchURL('https://tabemedia.co.uk/privacy'),
          ),
          const Divider(height: 1),
          _SettingsItem(
            icon: Icons.camera_front_outlined,
            title: 'Instagram',
            subtitle: '@vocadex.app',
            onTap: () => _launchURL('https://instagram.com/vocadex.app'),
          ),
          const Divider(height: 1),
          // _SettingsItem(
          //   icon: Icons.info_outline,
          //   title: 'About',
          //   subtitle: 'App version 0.1.0',
          //   onTap: () {
          //     // Show about dialog
          //     showAboutDialog(
          //       context: context,
          //       applicationName: 'Vocadex',
          //       applicationVersion: '1.0.0',
          //       applicationIcon: const FlutterLogo(size: 48),
          //       children: [
          //         const Text(
          //             'The vocabulary learning app that helps you master new words.'),
          //       ],
          //     );
          //   },
          // ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettingsSection(
      BuildContext context, WidgetRef ref) {
    final notificationPrefsAsync =
        ref.watch(userNotificationPreferencesNotifierProvider);
    final notificationEnabled = ref.watch(notificationEnabledProvider);

    return Column(
      children: [
        const _SectionHeader(title: 'Notification Settings'),
        Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              // Notifications Toggle
              notificationPrefsAsync.when(
                data: (prefs) => _SettingsItemWithSwitch(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Receive notifications on your device',
                  value: notificationEnabled,
                  onChanged: (value) async {
                    try {
                      await ref
                          .read(userNotificationPreferencesNotifierProvider
                              .notifier)
                          .updateNotifications(value);
                    } catch (e) {
                      if (context.mounted) {
                        showFailureToast(
                          context,
                          title: 'Error',
                          description: 'Failed to update notification settings',
                        );
                      }
                    }
                  },
                ),
                loading: () => _SettingsItemWithSwitch(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Receive notifications on your device',
                  value: true,
                  onChanged: null, // Disabled while loading
                ),
                error: (error, stack) => _SettingsItemWithSwitch(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  subtitle: 'Error loading settings',
                  value: true,
                  onChanged: null, // Disabled on error
                ),
              ),

              const Divider(height: 1),
              // Notification History
              _SettingsItem(
                icon: Icons.history,
                title: 'Notification History',
                subtitle: 'View all notifications you\'ve received',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          const UserNotificationHistoryScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAdminPanelSection(BuildContext context, WidgetRef ref) {
    final adminAccessAsync = ref.watch(adminAccessNotifierProvider);

    return adminAccessAsync.when(
      data: (isAdmin) {
        if (!isAdmin) return const SizedBox.shrink();

        return Column(
          children: [
            const _SectionHeader(title: 'Admin Panel'),
            Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  _SettingsItem(
                    icon: Icons.admin_panel_settings,
                    title: 'Notification Management',
                    subtitle: 'Send push notifications and configure triggers',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdminPanelScreen(),
                        ),
                      );
                    },
                  ),
                  const Divider(height: 1),
                  _SettingsItem(
                    icon: Icons.analytics_outlined,
                    title: 'Notification Analytics',
                    subtitle: 'View notification delivery and engagement stats',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdminPanelScreen(),
                        ),
                      );
                    },
                  ),
                  const Divider(height: 1),
                  _SettingsItem(
                    icon: Icons.bug_report_outlined,
                    title: 'Push Notification Debug',
                    subtitle: 'Debug push notification setup and tokens',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const NotificationDebugScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildSupportSection(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _SettingsItem(
            icon: Icons.feedback_outlined,
            title: AppStrings.sendFeedback,
            subtitle: AppStrings.feedbackSubtitle,
            onTap: () => showFeedbackDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountActions(
    BuildContext context,
    WidgetRef ref,
    AuthState authState,
    AsyncValue<bool> isAnonymousAsync,
  ) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: AuthButton(
            label: 'Sign Out',
            icon: Icons.logout,
            isOutlined: true,
            onPressed: () => _handleSignOut(context, ref),
          ),
        ),
        const SizedBox(height: 16),
        isAnonymousAsync.when(
          data: (isAnonymous) => isAnonymous
              ? const SizedBox.shrink()
              : _buildDeleteAccountButton(context, ref),
          loading: () => const SizedBox.shrink(),
          error: (_, __) => const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildDeleteAccountButton(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: () => _showDeleteAccountConfirmation(context, ref),
        style: TextButton.styleFrom(
          foregroundColor: AppColors.failureLight,
        ),
        child: const Text('Delete Account'),
      ),
    );
  }

  Future<void> _handleSignOut(BuildContext context, WidgetRef ref) async {
    final controller = ref.read(authControllerProvider.notifier);

    try {
      await controller.signOut();
      // Navigation will be handled by the auth state listener in the app
      // If we reach here without exception, sign-out was successful
    } catch (e) {
      if (context.mounted) {
        showFailureToast(
          context,
          title: 'Signout Error',
          description: 'Failed to Signout',
        );
      }
    }
  }

  void _showDeleteAccountConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account?'),
        content: const Text(
          'This action cannot be undone. All your data including saved vocabulary cards will be permanently deleted.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _handleDeleteAccount(context, ref);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.failureLight,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleDeleteAccount(BuildContext context, WidgetRef ref) async {
    if (!context.mounted) return;

    final confirmed = await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => const _DeleteAccountConfirmationDialog(),
        ) ??
        false;

    // Proceed with account deletion if confirmed
    if (confirmed) {
      final controller = ref.read(authControllerProvider.notifier);

      try {
        await controller.deleteAccount();

        // Give a small delay to allow auth state to update
        await Future.delayed(const Duration(milliseconds: 500));

        // Navigate to onboarding screen after successful deletion
        if (context.mounted) {
          // Show success message briefly before navigation
          showSuccessToast(
            context,
            title: 'Account Deleted',
            description: 'Your account has been permanently deleted',
          );

          // Navigate to onboarding screen starting at welcome and clear navigation stack
          context.goNamed(RouteNames.onboarding);
        }
      } catch (e) {
        if (context.mounted) {
          showFailureToast(
            context,
            title: 'Deletion Failed',
            description: 'Failed to delete account. Please try again.',
          );
        }
      }
    }
  }

  void _showUpdateProfileDialog(BuildContext context) {
    // Dialog for updating name and profile picture
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.bottomRight,
              children: [
                const CircleAvatar(
                  radius: 40,
                  child: Icon(Icons.person, size: 40),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.camera_alt,
                        color: AppColors.white, size: 16),
                    onPressed: () {
                      // Implement image picker
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'First Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Last Name',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              // Implement update logic
              if (context.mounted) {
                showSuccessToast(
                  context,
                  title: 'Profile Updated',
                  description: 'Your profile has been updated successfully',
                );
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showUpdateEmailDialog(BuildContext context) {
    // Dialog for updating email address
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Email Address'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Current Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 8),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'New Email Address',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              // Implement email update logic
              if (context.mounted) {
                showSuccessToast(
                  context,
                  title: 'Email Updated',
                  description:
                      'Your email address has been updated successfully',
                );
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog(BuildContext context) {
    // Dialog for changing password
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Current Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 8),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'New Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 8),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Confirm New Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              // Implement password change logic
              if (context.mounted) {
                showSuccessToast(
                  context,
                  title: 'Password Updated',
                  description: 'Your password has been updated successfully',
                );
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch $url');
    }
  }
}

class _SectionHeader extends StatelessWidget {
  final String title;

  const _SectionHeader({required this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }
}

class _SettingsItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;

  const _SettingsItem({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.getPrimaryColor(Theme.of(context).brightness)
                    .withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppColors.getPrimaryColor(Theme.of(context).brightness),
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.getTextColor(
                              Theme.of(context).brightness),
                        ),
                  ),
                  if (subtitle != null)
                    Text(
                      subtitle!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.getTextColor(
                                    Theme.of(context).brightness)
                                .withAlpha(153),
                          ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: AppColors.getTextColor(Theme.of(context).brightness)
                  .withAlpha(128),
            ),
          ],
        ),
      ),
    );
  }
}

class _SettingsItemWithSwitch extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final bool value;
  final ValueChanged<bool>? onChanged;

  const _SettingsItemWithSwitch({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.value,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.getPrimaryColor(Theme.of(context).brightness)
                  .withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppColors.getPrimaryColor(Theme.of(context).brightness),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColors.getTextColor(
                            Theme.of(context).brightness),
                      ),
                ),
                if (subtitle != null)
                  Text(
                    subtitle!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.getTextColor(
                                  Theme.of(context).brightness)
                              .withAlpha(153),
                        ),
                  ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor:
                AppColors.getPrimaryColor(Theme.of(context).brightness),
          ),
        ],
      ),
    );
  }
}

class _LoadingProfileHeader extends StatelessWidget {
  const _LoadingProfileHeader();

  @override
  Widget build(BuildContext context) {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }
}

class _GuestProfileHeader extends StatelessWidget {
  const _GuestProfileHeader();

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 36,
                  backgroundColor:
                      AppColors.getPrimaryColor(Theme.of(context).brightness)
                          .withAlpha(51),
                  child: Icon(
                    Icons.person,
                    size: 36,
                    color:
                        AppColors.getPrimaryColor(Theme.of(context).brightness),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Guest User',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.getTextColor(
                                  Theme.of(context).brightness),
                            ),
                      ),
                      Text(
                        'Sign in to access all features',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.getTextColor(
                                      Theme.of(context).brightness)
                                  .withAlpha(153),
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      gradient: const LinearGradient(
                        colors: [
                          AppColors.gradientButton1,
                          AppColors.gradientButton2
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: ElevatedButton(
                      onPressed: () => context.pushNamed(RouteNames.login),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: AppColors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Sign In'),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => context.pushNamed(RouteNames.signup),
                    style: OutlinedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Create Account'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _ErrorProfileHeader extends StatelessWidget {
  final String message;

  const _ErrorProfileHeader({required this.message});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.failureLight.withAlpha(26),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            const Icon(
              Icons.error_outline,
              color: AppColors.failureLight,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              'Error loading profile',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.failureLight,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(color: AppColors.failureLight),
            ),
            const SizedBox(height: 16),
            OutlinedButton(
              onPressed: () => context.goNamed(RouteNames.login),
              child: const Text('Go to Login'),
            ),
          ],
        ),
      ),
    );
  }
}

class _DeleteAccountConfirmationDialog extends StatefulWidget {
  const _DeleteAccountConfirmationDialog();

  @override
  State<_DeleteAccountConfirmationDialog> createState() =>
      _DeleteAccountConfirmationDialogState();
}

class _DeleteAccountConfirmationDialogState
    extends State<_DeleteAccountConfirmationDialog> {
  late final TextEditingController _confirmationController;
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _confirmationController = TextEditingController();
  }

  @override
  void dispose() {
    _confirmationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Confirm Account Deletion'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'This action cannot be undone. All your data including saved vocabulary cards will be permanently deleted.',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            const Text(
              'To confirm account deletion, please type "DELETE" below:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _confirmationController,
              decoration: const InputDecoration(
                labelText: 'Type DELETE to confirm',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.warning),
              ),
              enabled: !_isDeleting,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isDeleting ? null : () => Navigator.pop(context, false),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _isDeleting
              ? null
              : () async {
                  // Validate confirmation text
                  final confirmText = _confirmationController.text.trim();

                  if (confirmText.toLowerCase() != 'delete') {
                    showFailureToast(
                      context,
                      title: 'Error',
                      description: 'Please type "DELETE" to confirm',
                    );
                    return;
                  }

                  setState(() {
                    _isDeleting = true;
                  });

                  // No authentication required, proceed directly to deletion
                  if (context.mounted) {
                    Navigator.pop(context, true);
                  }
                },
          style: TextButton.styleFrom(
            foregroundColor: AppColors.failureLight,
          ),
          child: _isDeleting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Delete Account'),
        ),
      ],
    );
  }
}
