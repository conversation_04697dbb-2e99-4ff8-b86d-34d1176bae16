// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_notification_preferences_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationEnabledHash() =>
    r'8df225ed7526b100ccfc1d56e37c16b549cb2ee9';

/// Provider for getting notification preference
///
/// Copied from [notificationEnabled].
@ProviderFor(notificationEnabled)
final notificationEnabledProvider = AutoDisposeProvider<bool>.internal(
  notificationEnabled,
  name: r'notificationEnabledProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationEnabledHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationEnabledRef = AutoDisposeProviderRef<bool>;
String _$userNotificationPreferencesNotifierHash() =>
    r'ace6d5e0989a300adaa01f75c4d05681d967d86b';

/// See also [UserNotificationPreferencesNotifier].
@ProviderFor(UserNotificationPreferencesNotifier)
final userNotificationPreferencesNotifierProvider =
    AutoDisposeAsyncNotifierProvider<UserNotificationPreferencesNotifier,
        Map<String, bool>>.internal(
  UserNotificationPreferencesNotifier.new,
  name: r'userNotificationPreferencesNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userNotificationPreferencesNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserNotificationPreferencesNotifier
    = AutoDisposeAsyncNotifier<Map<String, bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
