import 'package:freezed_annotation/freezed_annotation.dart';

part 'trigger_event_model.freezed.dart';
part 'trigger_event_model.g.dart';

@freezed
class TriggerEvent with _$TriggerEvent {
  const factory TriggerEvent({
    required String id,
    required String name,
    required String description,
    required TriggerType type,
    @Default(true) bool enabled,
    required String title,
    required String message,
    TriggerSchedule? schedule,
    Map<String, dynamic>? conditions,
    DateTime? lastTriggered,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _TriggerEvent;

  const TriggerEvent._();

  factory TriggerEvent.fromJson(Map<String, dynamic> json) =>
      _$TriggerEventFromJson(json);

  factory TriggerEvent.fromFirestore(Map<String, dynamic> data, String id) {
    return TriggerEvent(
      id: id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      type: TriggerType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => TriggerType.dailyReminder,
      ),
      enabled: data['enabled'] ?? true,
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      schedule: data['schedule'] != null
          ? TriggerSchedule.fromJson(
              Map<String, dynamic>.from(data['schedule']))
          : null,
      conditions: data['conditions'] != null
          ? Map<String, dynamic>.from(data['conditions'])
          : null,
      lastTriggered: data['lastTriggered']?.toDate(),
      createdAt: data['createdAt']?.toDate(),
      updatedAt: data['updatedAt']?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'type': type.name,
      'enabled': enabled,
      'title': title,
      'message': message,
      'schedule': schedule?.toJson(),
      'conditions': conditions,
      'lastTriggered': lastTriggered,
      'createdAt': createdAt ?? DateTime.now(),
      'updatedAt': DateTime.now(),
    };
  }
}

@freezed
class TriggerSchedule with _$TriggerSchedule {
  const factory TriggerSchedule({
    required ScheduleType type,
    String? time, // HH:mm format
    List<int>? daysOfWeek, // 1-7 (Monday-Sunday)
    int? intervalHours,
    int? intervalDays,
  }) = _TriggerSchedule;

  factory TriggerSchedule.fromJson(Map<String, dynamic> json) =>
      _$TriggerScheduleFromJson(json);
}

enum TriggerType {
  dailyReminder,
  weeklyGoal,
  streakReminder,
  dailyWord,
  lowDiamonds,
  masteryProgress,
  inactivityReminder,
  achievementUnlock,
  wordRecommendation,
}

enum ScheduleType {
  daily,
  weekly,
  interval,
  once,
}
