import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/notifications/services/notification_service.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

/// Debug screen for testing and monitoring push notifications
class NotificationDebugScreen extends ConsumerStatefulWidget {
  const NotificationDebugScreen({super.key});

  @override
  ConsumerState<NotificationDebugScreen> createState() =>
      _NotificationDebugScreenState();
}

class _NotificationDebugScreenState
    extends ConsumerState<NotificationDebugScreen> {
  Map<String, dynamic>? _tokenStatus;
  String? _fcmToken;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _refreshStatus();
  }

  Future<void> _refreshStatus() async {
    setState(() => _isLoading = true);

    try {
      final notificationService = ref.read(notificationServiceProvider);

      // Get token status
      final status = await notificationService.getDebugTokenStatus();

      // Get FCM token
      final token = await notificationService.getFCMToken();

      setState(() {
        _tokenStatus = status;
        _fcmToken = token;
      });
    } catch (e) {
      debugPrint('Error refreshing status: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _refreshToken() async {
    setState(() => _isLoading = true);

    try {
      final notificationService = ref.read(notificationServiceProvider);
      final newToken = await notificationService.refreshFCMToken();

      setState(() {
        _fcmToken = newToken;
      });

      // Refresh status after token refresh
      await _refreshStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(newToken != null
                ? 'Token refreshed successfully'
                : 'Failed to refresh token'),
            backgroundColor: newToken != null ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing token: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _copyToken() {
    if (_fcmToken != null) {
      Clipboard.setData(ClipboardData(text: _fcmToken!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('FCM Token copied to clipboard'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GradientScaffold(
      appBar: AppBar(
        title: const Text(
          'Push Notification Debug',
          style: TextStyle(
            color: AppColors.textLight,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textLight),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshStatus,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // FCM Token Section
                  _buildSection(
                    title: 'FCM Token',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_fcmToken != null) ...[
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _fcmToken!,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              ElevatedButton.icon(
                                onPressed: _copyToken,
                                icon: const Icon(Icons.copy),
                                label: const Text('Copy Token'),
                              ),
                              const SizedBox(width: 8),
                              ElevatedButton.icon(
                                onPressed: _refreshToken,
                                icon: const Icon(Icons.refresh),
                                label: const Text('Refresh Token'),
                              ),
                            ],
                          ),
                        ] else ...[
                          const Text('No FCM token available'),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: _refreshToken,
                            child: const Text('Get Token'),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Token Status Section
                  _buildSection(
                    title: 'Token Status',
                    child: _tokenStatus != null
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: _tokenStatus!.entries
                                .map((entry) => Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            width: 150,
                                            child: Text(
                                              '${entry.key}:',
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                          Expanded(
                                            child: Text(
                                              entry.value.toString(),
                                              style: TextStyle(
                                                color: _getStatusColor(
                                                    entry.key, entry.value),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ))
                                .toList(),
                          )
                        : const Text('No status information available'),
                  ),

                  const SizedBox(height: 24),

                  // Actions Section
                  _buildSection(
                    title: 'Debug Actions',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _refreshStatus,
                          icon: const Icon(Icons.info),
                          label: const Text('Refresh Status'),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Use this screen to monitor your push notification setup. '
                          'Check the console logs for detailed debugging information.',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            child,
          ],
        ),
      ),
    );
  }

  Color? _getStatusColor(String key, dynamic value) {
    if (key.contains('Error') || key.contains('error')) {
      return Colors.red;
    }
    if (key.contains('Available') && value == true) {
      return Colors.green;
    }
    if (key.contains('Available') && value == false) {
      return Colors.red;
    }
    if (key == 'authorizationStatus' &&
        value.toString().contains('authorized')) {
      return Colors.green;
    }
    return null;
  }
}
