import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/services/firebase_service.dart';

part 'word_recommendation_service.g.dart';

@riverpod
WordRecommendationService wordRecommendationService(Ref ref) {
  return WordRecommendationService();
}

/// Provider for getting a single word recommendation
@riverpod
Future<VocabCard?> wordRecommendation(Ref ref) async {
  final service = ref.watch(wordRecommendationServiceProvider);
  return await service.getWordRecommendation();
}

/// Provider for getting multiple word recommendations
@riverpod
Future<List<VocabCard>> multipleWordRecommendations(Ref ref,
    {int count = 3}) async {
  final service = ref.watch(wordRecommendationServiceProvider);
  return await service.getMultipleWordRecommendations(count: count);
}

/// Provider for checking if user has words needing practice
@riverpod
Future<bool> hasWordsNeedingPractice(Ref ref) async {
  final service = ref.watch(wordRecommendationServiceProvider);
  return await service.hasWordsNeedingPractice();
}

/// Provider for mastery statistics
@riverpod
Future<Map<String, int>> masteryStatistics(Ref ref) async {
  final service = ref.watch(wordRecommendationServiceProvider);
  return await service.getMasteryStatistics();
}

/// Service for recommending words with low mastery levels for practice
class WordRecommendationService {
  final FirebaseService _firebaseService = FirebaseService();

  /// Get a random word recommendation from low mastery words
  /// Returns words with mastery levels 1-3 (Wild category)
  Future<VocabCard?> getWordRecommendation() async {
    try {
      // Fetch all vocabulary cards
      final allCards = await _firebaseService.fetchVocabulary();

      if (allCards.isEmpty) {
        debugPrint('No vocabulary cards found for word recommendation');
        return null;
      }

      // Filter for low mastery words (Wild category: levels 1-3)
      final lowMasteryWords =
          allCards.where((card) => card.masteryLevel <= 3).toList();

      if (lowMasteryWords.isEmpty) {
        debugPrint('No low mastery words found for recommendation');
        return null;
      }

      // Sort by mastery level (lowest first) and last reviewed date (oldest first)
      lowMasteryWords.sort((a, b) {
        // First priority: lower mastery level
        final masteryComparison = a.masteryLevel.compareTo(b.masteryLevel);
        if (masteryComparison != 0) return masteryComparison;

        // Second priority: older last reviewed date (or never reviewed)
        final aLastReviewed = a.lastReviewedAt ?? DateTime(1970);
        final bLastReviewed = b.lastReviewedAt ?? DateTime(1970);
        return aLastReviewed.compareTo(bLastReviewed);
      });

      // Take the top 5 candidates (lowest mastery, oldest reviewed)
      final topCandidates = lowMasteryWords.take(5).toList();

      // Randomly select from top candidates to add some variety
      final random = Random();
      final selectedCard = topCandidates[random.nextInt(topCandidates.length)];

      debugPrint(
          'Recommended word: ${selectedCard.word} (mastery: ${selectedCard.masteryLevel})');
      return selectedCard;
    } catch (e) {
      debugPrint('Error getting word recommendation: $e');
      return null;
    }
  }

  /// Get multiple word recommendations for practice
  Future<List<VocabCard>> getMultipleWordRecommendations(
      {int count = 3}) async {
    try {
      final allCards = await _firebaseService.fetchVocabulary();

      if (allCards.isEmpty) {
        return [];
      }

      // Filter for low mastery words
      final lowMasteryWords =
          allCards.where((card) => card.masteryLevel <= 3).toList();

      if (lowMasteryWords.isEmpty) {
        return [];
      }

      // Sort by mastery level and last reviewed date
      lowMasteryWords.sort((a, b) {
        final masteryComparison = a.masteryLevel.compareTo(b.masteryLevel);
        if (masteryComparison != 0) return masteryComparison;

        final aLastReviewed = a.lastReviewedAt ?? DateTime(1970);
        final bLastReviewed = b.lastReviewedAt ?? DateTime(1970);
        return aLastReviewed.compareTo(bLastReviewed);
      });

      // Return the requested number of recommendations
      return lowMasteryWords.take(count).toList();
    } catch (e) {
      debugPrint('Error getting multiple word recommendations: $e');
      return [];
    }
  }

  /// Check if user has words that need practice (low mastery)
  Future<bool> hasWordsNeedingPractice() async {
    try {
      final allCards = await _firebaseService.fetchVocabulary();
      return allCards.any((card) => card.masteryLevel <= 3);
    } catch (e) {
      debugPrint('Error checking words needing practice: $e');
      return false;
    }
  }

  /// Get statistics about mastery levels in user's deck
  Future<Map<String, int>> getMasteryStatistics() async {
    try {
      final allCards = await _firebaseService.fetchVocabulary();

      int wildCount = 0;
      int tamedCount = 0;
      int masteredCount = 0;

      for (final card in allCards) {
        if (card.masteryLevel <= 3) {
          wildCount++;
        } else if (card.masteryLevel <= 6) {
          tamedCount++;
        } else {
          masteredCount++;
        }
      }

      return {
        'wild': wildCount,
        'tamed': tamedCount,
        'mastered': masteredCount,
        'total': allCards.length,
      };
    } catch (e) {
      debugPrint('Error getting mastery statistics: $e');
      return {
        'wild': 0,
        'tamed': 0,
        'mastered': 0,
        'total': 0,
      };
    }
  }

  /// Get a personalized recommendation message based on the word
  String getRecommendationMessage(VocabCard word) {
    final messages = [
      'Time to practice "${word.word}"! This word needs some attention.',
      'Let\'s work on "${word.word}" today. Practice makes perfect!',
      'Ready to master "${word.word}"? Give it another try!',
      'Your word "${word.word}" is waiting for practice. Let\'s go!',
      'Boost your vocabulary with "${word.word}" - practice time!',
    ];

    final random = Random();
    return messages[random.nextInt(messages.length)];
  }
}
