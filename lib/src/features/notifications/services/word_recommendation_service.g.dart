// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'word_recommendation_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$wordRecommendationServiceHash() =>
    r'ca6f9bc3619737a9f612d3c8a68ff343f7aa5047';

/// See also [wordRecommendationService].
@ProviderFor(wordRecommendationService)
final wordRecommendationServiceProvider =
    AutoDisposeProvider<WordRecommendationService>.internal(
  wordRecommendationService,
  name: r'wordRecommendationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$wordRecommendationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WordRecommendationServiceRef
    = AutoDisposeProviderRef<WordRecommendationService>;
String _$wordRecommendationHash() =>
    r'7206978116f8020940f49521289d7bd3012cc780';

/// Provider for getting a single word recommendation
///
/// Copied from [wordRecommendation].
@ProviderFor(wordRecommendation)
final wordRecommendationProvider =
    AutoDisposeFutureProvider<VocabCard?>.internal(
  wordRecommendation,
  name: r'wordRecommendationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$wordRecommendationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WordRecommendationRef = AutoDisposeFutureProviderRef<VocabCard?>;
String _$multipleWordRecommendationsHash() =>
    r'c8912cf38a3822e8f1516438be58858b083e7d00';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for getting multiple word recommendations
///
/// Copied from [multipleWordRecommendations].
@ProviderFor(multipleWordRecommendations)
const multipleWordRecommendationsProvider = MultipleWordRecommendationsFamily();

/// Provider for getting multiple word recommendations
///
/// Copied from [multipleWordRecommendations].
class MultipleWordRecommendationsFamily
    extends Family<AsyncValue<List<VocabCard>>> {
  /// Provider for getting multiple word recommendations
  ///
  /// Copied from [multipleWordRecommendations].
  const MultipleWordRecommendationsFamily();

  /// Provider for getting multiple word recommendations
  ///
  /// Copied from [multipleWordRecommendations].
  MultipleWordRecommendationsProvider call({
    int count = 3,
  }) {
    return MultipleWordRecommendationsProvider(
      count: count,
    );
  }

  @override
  MultipleWordRecommendationsProvider getProviderOverride(
    covariant MultipleWordRecommendationsProvider provider,
  ) {
    return call(
      count: provider.count,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'multipleWordRecommendationsProvider';
}

/// Provider for getting multiple word recommendations
///
/// Copied from [multipleWordRecommendations].
class MultipleWordRecommendationsProvider
    extends AutoDisposeFutureProvider<List<VocabCard>> {
  /// Provider for getting multiple word recommendations
  ///
  /// Copied from [multipleWordRecommendations].
  MultipleWordRecommendationsProvider({
    int count = 3,
  }) : this._internal(
          (ref) => multipleWordRecommendations(
            ref as MultipleWordRecommendationsRef,
            count: count,
          ),
          from: multipleWordRecommendationsProvider,
          name: r'multipleWordRecommendationsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$multipleWordRecommendationsHash,
          dependencies: MultipleWordRecommendationsFamily._dependencies,
          allTransitiveDependencies:
              MultipleWordRecommendationsFamily._allTransitiveDependencies,
          count: count,
        );

  MultipleWordRecommendationsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.count,
  }) : super.internal();

  final int count;

  @override
  Override overrideWith(
    FutureOr<List<VocabCard>> Function(MultipleWordRecommendationsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: MultipleWordRecommendationsProvider._internal(
        (ref) => create(ref as MultipleWordRecommendationsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        count: count,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VocabCard>> createElement() {
    return _MultipleWordRecommendationsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MultipleWordRecommendationsProvider && other.count == count;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, count.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MultipleWordRecommendationsRef
    on AutoDisposeFutureProviderRef<List<VocabCard>> {
  /// The parameter `count` of this provider.
  int get count;
}

class _MultipleWordRecommendationsProviderElement
    extends AutoDisposeFutureProviderElement<List<VocabCard>>
    with MultipleWordRecommendationsRef {
  _MultipleWordRecommendationsProviderElement(super.provider);

  @override
  int get count => (origin as MultipleWordRecommendationsProvider).count;
}

String _$hasWordsNeedingPracticeHash() =>
    r'6efff5ef77164bac7735ca90708e09f90497487d';

/// Provider for checking if user has words needing practice
///
/// Copied from [hasWordsNeedingPractice].
@ProviderFor(hasWordsNeedingPractice)
final hasWordsNeedingPracticeProvider =
    AutoDisposeFutureProvider<bool>.internal(
  hasWordsNeedingPractice,
  name: r'hasWordsNeedingPracticeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasWordsNeedingPracticeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HasWordsNeedingPracticeRef = AutoDisposeFutureProviderRef<bool>;
String _$masteryStatisticsHash() => r'b982c6ad4bdd96cc5b8ee29c27c48f0dbf442f7c';

/// Provider for mastery statistics
///
/// Copied from [masteryStatistics].
@ProviderFor(masteryStatistics)
final masteryStatisticsProvider =
    AutoDisposeFutureProvider<Map<String, int>>.internal(
  masteryStatistics,
  name: r'masteryStatisticsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$masteryStatisticsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MasteryStatisticsRef = AutoDisposeFutureProviderRef<Map<String, int>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
