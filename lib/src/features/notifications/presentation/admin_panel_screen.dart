import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';

import 'package:vocadex/src/features/notifications/presentation/widgets/send_notification_card.dart';
import 'package:vocadex/src/features/notifications/presentation/widgets/notification_history_card.dart';
import 'package:vocadex/src/features/notifications/presentation/widgets/trigger_management_card.dart';
import 'package:vocadex/src/features/notifications/presentation/widgets/notification_analytics_card.dart';

class AdminPanelScreen extends ConsumerStatefulWidget {
  const AdminPanelScreen({super.key});

  @override
  ConsumerState<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends ConsumerState<AdminPanelScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GradientScaffold(
      appBar: AppBar(
        title: const Text(
          'Admin Panel',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textLight,
          ),
        ),
        backgroundColor: AppColors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textLight),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.primaryLight,
          labelColor: AppColors.textLight,
          unselectedLabelColor: AppColors.textLight.withValues(alpha: 0.6),
          tabs: const [
            Tab(
              icon: Icon(Icons.send),
              text: 'Send Notification',
            ),
            Tab(
              icon: Icon(Icons.settings),
              text: 'Triggers',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: 'History',
            ),
            Tab(
              icon: Icon(Icons.analytics),
              text: 'Analytics',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          _SendNotificationTab(),
          _TriggerManagementTab(),
          _NotificationHistoryTab(),
          _NotificationAnalyticsTab(),
        ],
      ),
    );
  }
}

class _SendNotificationTab extends ConsumerWidget {
  const _SendNotificationTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Send Push Notification',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textLight,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Send instant notifications to all app users',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textLight.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 24),
          SendNotificationCard(),
        ],
      ),
    );
  }
}

class _TriggerManagementTab extends ConsumerWidget {
  const _TriggerManagementTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification Triggers',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textLight,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Configure automatic notification triggers',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textLight.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 24),
          TriggerManagementCard(),
        ],
      ),
    );
  }
}

class _NotificationHistoryTab extends ConsumerWidget {
  const _NotificationHistoryTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification History',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textLight,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'View sent notifications and delivery stats',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textLight.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 24),
          NotificationHistoryCard(),
        ],
      ),
    );
  }
}

class _NotificationAnalyticsTab extends ConsumerWidget {
  const _NotificationAnalyticsTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notification Analytics',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textLight,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'View notification performance and engagement metrics',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textLight.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 24),
          NotificationAnalyticsCard(),
        ],
      ),
    );
  }
}
