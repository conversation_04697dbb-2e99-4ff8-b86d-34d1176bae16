import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vocadex/src/features/notifications/services/admin_notification_service.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';
import 'package:vocadex/src/features/notifications/presentation/widgets/edit_trigger_dialog.dart';
import 'package:vocadex/src/features/notifications/providers/notification_config_provider.dart';
import 'package:vocadex/src/common/widgets/toasts.dart';

class TriggerManagementCard extends ConsumerStatefulWidget {
  const TriggerManagementCard({super.key});

  @override
  ConsumerState<TriggerManagementCard> createState() =>
      _TriggerManagementCardState();
}

class _TriggerManagementCardState extends ConsumerState<TriggerManagementCard> {
  bool _isInitializing = false;

  Future<void> _initializeDefaultTriggers() async {
    setState(() {
      _isInitializing = true;
    });

    try {
      final adminService = ref.read(adminNotificationServiceProvider);
      await adminService.createDefaultTriggerEvents();

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: 'Default triggers created successfully!',
        );
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Failed to create default triggers: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final adminService = ref.watch(adminNotificationServiceProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.settings,
                  color: Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Trigger Management',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed:
                      _isInitializing ? null : _initializeDefaultTriggers,
                  icon: _isInitializing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.add, size: 16),
                  label: const Text('Setup Defaults'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            StreamBuilder<List<TriggerEvent>>(
              stream: adminService.watchTriggerEvents(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Error loading triggers: ${snapshot.error}',
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  );
                }

                final triggers = snapshot.data ?? [];

                if (triggers.isEmpty) {
                  return Center(
                    child: Column(
                      children: [
                        const Icon(
                          Icons.notifications_off,
                          color: Colors.grey,
                          size: 48,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'No triggers configured yet',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Click "Setup Defaults" to create basic triggers',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: triggers.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 12),
                  itemBuilder: (context, index) {
                    final trigger = triggers[index];
                    return _TriggerItem(
                      trigger: trigger,
                      onToggle: (enabled) => _toggleTrigger(trigger, enabled),
                      onEdit: () => _editTrigger(trigger),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleTrigger(TriggerEvent trigger, bool enabled) async {
    try {
      final notificationConfig =
          ref.read(notificationConfigNotifierProvider.notifier);
      final updatedTrigger = trigger.copyWith(enabled: enabled);
      await notificationConfig.updateTriggerEvent(updatedTrigger);

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: enabled ? 'Trigger enabled' : 'Trigger disabled',
        );
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Failed to update trigger: $e',
        );
      }
    }
  }

  Future<void> _editTrigger(TriggerEvent trigger) async {
    await showDialog<void>(
      context: context,
      builder: (context) => EditTriggerDialog(
        trigger: trigger,
        onSave: (updatedTrigger) => _saveTrigger(updatedTrigger),
      ),
    );
  }

  Future<void> _saveTrigger(TriggerEvent trigger) async {
    try {
      final notificationConfig =
          ref.read(notificationConfigNotifierProvider.notifier);
      await notificationConfig.updateTriggerEvent(trigger);

      if (mounted) {
        showSuccessToast(
          context,
          title: 'Success',
          description: 'Trigger updated successfully!',
        );
      }
    } catch (e) {
      if (mounted) {
        showFailureToast(
          context,
          title: 'Error',
          description: 'Failed to update trigger: $e',
        );
      }
    }
  }
}

class _TriggerItem extends StatelessWidget {
  final TriggerEvent trigger;
  final Function(bool) onToggle;
  final VoidCallback onEdit;

  const _TriggerItem({
    required this.trigger,
    required this.onToggle,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: trigger.enabled ? Colors.green : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getTriggerIcon(),
                color: trigger.enabled ? Colors.green : Colors.grey,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  trigger.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              IconButton(
                onPressed: onEdit,
                icon: const Icon(Icons.edit, size: 18),
                tooltip: 'Edit trigger',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
              ),
              Switch(
                value: trigger.enabled,
                onChanged: onToggle,
                activeColor: Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            trigger.description,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '"${trigger.message}"',
              style: const TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Colors.blue,
              ),
            ),
          ),
          if (trigger.schedule != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  _getScheduleText(),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  IconData _getTriggerIcon() {
    switch (trigger.type) {
      case TriggerType.dailyReminder:
        return Icons.today;
      case TriggerType.weeklyGoal:
        return Icons.flag;
      case TriggerType.streakReminder:
        return Icons.local_fire_department;
      case TriggerType.dailyWord:
        return Icons.book;
      case TriggerType.lowDiamonds:
        return Icons.diamond;
      case TriggerType.masteryProgress:
        return Icons.trending_up;
      case TriggerType.inactivityReminder:
        return Icons.notifications_active;
      case TriggerType.achievementUnlock:
        return Icons.emoji_events;
    }
  }

  String _getScheduleText() {
    final schedule = trigger.schedule!;
    switch (schedule.type) {
      case ScheduleType.daily:
        return 'Daily at ${schedule.time ?? 'N/A'}';
      case ScheduleType.weekly:
        return 'Weekly on ${_getDayName(schedule.daysOfWeek?.first ?? 1)} at ${schedule.time ?? 'N/A'}';
      case ScheduleType.interval:
        return 'Every ${schedule.intervalHours ?? schedule.intervalDays ?? 'N/A'} ${schedule.intervalHours != null ? 'hours' : 'days'}';
      case ScheduleType.once:
        return 'One time';
    }
  }

  String _getDayName(int dayOfWeek) {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];
    return days[dayOfWeek - 1];
  }
}
