import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vocadex/src/features/notifications/models/trigger_event_model.dart';

class EditTriggerDialog extends StatefulWidget {
  final TriggerEvent trigger;
  final Function(TriggerEvent) onSave;

  const EditTriggerDialog({
    super.key,
    required this.trigger,
    required this.onSave,
  });

  @override
  State<EditTriggerDialog> createState() => _EditTriggerDialogState();
}

class _EditTriggerDialogState extends State<EditTriggerDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _titleController;
  late TextEditingController _messageController;
  late TextEditingController _timeController;
  late TextEditingController _intervalHoursController;
  late TextEditingController _intervalDaysController;
  
  late bool _enabled;
  late ScheduleType _scheduleType;
  late List<int> _selectedDaysOfWeek;
  TimeOfDay? _selectedTime;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final trigger = widget.trigger;
    final schedule = trigger.schedule;

    _nameController = TextEditingController(text: trigger.name);
    _descriptionController = TextEditingController(text: trigger.description);
    _titleController = TextEditingController(text: trigger.title);
    _messageController = TextEditingController(text: trigger.message);
    _timeController = TextEditingController(text: schedule?.time ?? '');
    _intervalHoursController = TextEditingController(
      text: schedule?.intervalHours?.toString() ?? '',
    );
    _intervalDaysController = TextEditingController(
      text: schedule?.intervalDays?.toString() ?? '',
    );

    _enabled = trigger.enabled;
    _scheduleType = schedule?.type ?? ScheduleType.daily;
    _selectedDaysOfWeek = List.from(schedule?.daysOfWeek ?? [1]);

    // Parse time if available
    if (schedule?.time != null) {
      final timeParts = schedule!.time!.split(':');
      if (timeParts.length == 2) {
        final hour = int.tryParse(timeParts[0]);
        final minute = int.tryParse(timeParts[1]);
        if (hour != null && minute != null) {
          _selectedTime = TimeOfDay(hour: hour, minute: minute);
        }
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _titleController.dispose();
    _messageController.dispose();
    _timeController.dispose();
    _intervalHoursController.dispose();
    _intervalDaysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Icon(Icons.edit, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Text(
                    'Edit Trigger',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Scrollable content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Enable/Disable
                      SwitchListTile(
                        title: const Text('Enabled'),
                        subtitle: const Text('Enable or disable this trigger'),
                        value: _enabled,
                        onChanged: (value) {
                          setState(() {
                            _enabled = value;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Basic Info
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Name',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: 'Notification Title',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a notification title';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _messageController,
                        decoration: const InputDecoration(
                          labelText: 'Notification Message',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a notification message';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),

                      // Schedule Configuration
                      const Text(
                        'Schedule Configuration',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      _buildScheduleConfiguration(),
                    ],
                  ),
                ),
              ),

              // Action buttons
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _saveTrigger,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Save'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScheduleConfiguration() {
    switch (_scheduleType) {
      case ScheduleType.daily:
        return _buildDailySchedule();
      case ScheduleType.weekly:
        return _buildWeeklySchedule();
      case ScheduleType.interval:
        return _buildIntervalSchedule();
      case ScheduleType.once:
        return _buildOnceSchedule();
    }
  }

  Widget _buildDailySchedule() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Daily Schedule'),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectTime,
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Time',
              border: OutlineInputBorder(),
              suffixIcon: Icon(Icons.access_time),
            ),
            child: Text(
              _selectedTime != null
                  ? _formatTime(_selectedTime!)
                  : 'Select time',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWeeklySchedule() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Weekly Schedule'),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectTime,
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Time',
              border: OutlineInputBorder(),
              suffixIcon: Icon(Icons.access_time),
            ),
            child: Text(
              _selectedTime != null
                  ? _formatTime(_selectedTime!)
                  : 'Select time',
            ),
          ),
        ),
        const SizedBox(height: 12),
        const Text('Days of Week'),
        const SizedBox(height: 8),
        _buildDaySelector(),
      ],
    );
  }

  Widget _buildIntervalSchedule() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Interval Schedule'),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _intervalHoursController,
                decoration: const InputDecoration(
                  labelText: 'Hours',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
            ),
            const SizedBox(width: 12),
            const Text('OR'),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _intervalDaysController,
                decoration: const InputDecoration(
                  labelText: 'Days',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOnceSchedule() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('One-time Schedule'),
        SizedBox(height: 8),
        Text(
          'This trigger will be executed once based on specific conditions.',
          style: TextStyle(color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildDaySelector() {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    return Wrap(
      spacing: 8,
      children: List.generate(7, (index) {
        final dayNumber = index + 1;
        final isSelected = _selectedDaysOfWeek.contains(dayNumber);
        
        return FilterChip(
          label: Text(days[index]),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                if (!_selectedDaysOfWeek.contains(dayNumber)) {
                  _selectedDaysOfWeek.add(dayNumber);
                }
              } else {
                _selectedDaysOfWeek.remove(dayNumber);
              }
            });
          },
        );
      }),
    );
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? const TimeOfDay(hour: 9, minute: 0),
    );
    
    if (picked != null) {
      setState(() {
        _selectedTime = picked;
        _timeController.text = _formatTime(picked);
      });
    }
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  void _saveTrigger() {
    if (!_formKey.currentState!.validate()) return;

    // Build updated schedule
    TriggerSchedule? schedule;
    switch (_scheduleType) {
      case ScheduleType.daily:
        if (_selectedTime != null) {
          schedule = TriggerSchedule(
            type: ScheduleType.daily,
            time: _formatTime(_selectedTime!),
          );
        }
        break;
      case ScheduleType.weekly:
        if (_selectedTime != null && _selectedDaysOfWeek.isNotEmpty) {
          schedule = TriggerSchedule(
            type: ScheduleType.weekly,
            time: _formatTime(_selectedTime!),
            daysOfWeek: _selectedDaysOfWeek,
          );
        }
        break;
      case ScheduleType.interval:
        final hours = int.tryParse(_intervalHoursController.text);
        final days = int.tryParse(_intervalDaysController.text);
        if (hours != null || days != null) {
          schedule = TriggerSchedule(
            type: ScheduleType.interval,
            intervalHours: hours,
            intervalDays: days,
          );
        }
        break;
      case ScheduleType.once:
        schedule = const TriggerSchedule(type: ScheduleType.once);
        break;
    }

    // Create updated trigger
    final updatedTrigger = widget.trigger.copyWith(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      title: _titleController.text.trim(),
      message: _messageController.text.trim(),
      enabled: _enabled,
      schedule: schedule,
      updatedAt: DateTime.now(),
    );

    widget.onSave(updatedTrigger);
    Navigator.of(context).pop();
  }
}
