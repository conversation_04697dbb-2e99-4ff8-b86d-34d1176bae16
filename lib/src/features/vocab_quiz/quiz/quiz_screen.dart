// lib/src/features/vocab_quiz/screens/quiz_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vocadex/src/core/theme/constants/constants_color.dart';
import 'package:vocadex/src/features/vocab_quiz/common/question_widget.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_model.dart';
import 'package:vocadex/src/features/vocab_quiz/common/quiz_providers.dart';
import 'package:vocadex/src/features/vocab_quiz/quiz/quiz_feedback_screen.dart';
import 'package:vocadex/src/features/dashboard/provider/refresh_providers.dart';
import 'package:vocadex/src/features/vocab_quiz/common/widgets/card_drag_and_drop_quiz.dart';
import 'package:vocadex/src/features/vocab_quiz/common/widgets/spell_word_question.dart';
import 'package:vocadex/src/features/vocab_quiz/common/widgets/true_false_question.dart';
import 'package:vocadex/src/features/vocab_deck/providers/vocab_providers.dart';

import 'package:just_audio/just_audio.dart';
import 'dart:async';

/// Screen for taking a quiz
class QuizScreen extends ConsumerStatefulWidget {
  const QuizScreen({super.key});

  @override
  ConsumerState<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends ConsumerState<QuizScreen>
    with SingleTickerProviderStateMixin {
  bool _showingFeedback = false;
  bool _canAdvance = true;
  bool _isAnimating = false;
  Key _questionKey = UniqueKey();
  QuizQuestion? _lastAnsweredQuestion;
  String? _lastUserAnswer;
  String? _lastUserAnswerDefinition;

  // For timer
  Timer? _timer;
  int _remainingSeconds = 0;
  bool _isTimerRunning = false;

  // For animation
  late AnimationController _pageController;
  late Animation<double> _pageAnimation;

  // For sound effects
  late AudioPlayer _audioPlayer;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _preloadSounds();

    _pageController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pageAnimation = CurvedAnimation(
      parent: _pageController,
      curve: Curves.easeInOut,
    );

    _pageController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
          _canAdvance = true;
        });
      }
    });

    // Initialize the timer if the quiz is timed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final quiz = ref.read(currentQuizProvider);
      if (quiz != null && quiz.isTimed && quiz.timeLimit != null) {
        if (quiz.isChallenge || quiz.mode == QuizMode.challenge) {
          // For challenge quizzes, show a countdown before starting
          _showCountdown(quiz.timeLimit!);
        } else {
          // For regular timed quizzes, start the timer immediately
          _startTimer(quiz.timeLimit!);
        }
      }
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _pageController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer(int seconds) {
    _remainingSeconds = seconds;
    _isTimerRunning = true;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _isTimerRunning = false;
          timer.cancel();
          _onTimeUp();
        }
      });
    });
  }

  void _onTimeUp() {
    // Handle time-up event - submit the current quiz as is
    final quiz = ref.read(currentQuizProvider);
    if (quiz != null) {
      ref.read(currentQuizProvider.notifier).state = quiz.copyWith(
        isCompleted: true,
      );

      // Trigger dashboard refresh to update resource values
      ref
          .read(dashboardRefreshProvider.notifier)
          .update((state) => DateTime.now());

      // Navigate to results screen
      if (mounted) {
        context.pushReplacement('/quiz/results', extra: quiz);
      }
    }
  }

  Future<void> _preloadSounds() async {
    await _audioPlayer.setAsset('assets/sounds/correct.mp3');
    await _audioPlayer.setAsset('assets/sounds/incorrect.mp3');
  }

  Future<void> _playSound(String soundType) async {
    try {
      await _audioPlayer.setAsset('assets/sounds/$soundType.mp3');
      await _audioPlayer.play();
    } catch (e) {
      debugPrint('Error playing sound: $e');
    }
  }

  Future<bool> _onWillPop() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('End Quiz?'),
        content: const Text(
            'Are you sure you want to end this quiz? Your progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('End Quiz'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  // Show a countdown before starting a challenge quiz
  Future<void> _showCountdown(int totalSeconds) async {
    // Show 3-2-1 countdown before starting the actual timer
    int countdownSeconds = 3;

    // Show a dialog with the countdown
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(
            builder: (context, setState) {
              // Start the countdown
              Timer(const Duration(seconds: 1), () {
                if (countdownSeconds > 1) {
                  setState(() {
                    countdownSeconds--;
                  });
                } else {
                  // Close the dialog and start the quiz timer
                  Navigator.of(context).pop();
                  _startTimer(totalSeconds);
                }
              });

              return AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '$countdownSeconds',
                      style: const TextStyle(
                        fontSize: 72,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text('Get ready!'),
                  ],
                ),
              );
            },
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final quiz = ref.watch(currentQuizProvider);
    final isLoading = ref.watch(quizLoadingProvider);
    final error = ref.watch(quizErrorProvider);

    final currentIndex = ref.watch(currentQuestionIndexProvider);

    // Show error state if there's an error
    if (error != null) {
      debugPrint('❌ QUIZ_SCREEN: Error occurred: $error');
      return Scaffold(
        appBar: AppBar(title: const Text('Quiz Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Quiz Generation Failed',
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  error,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    // Show loading state if quiz is null or loading
    if (quiz == null || isLoading) {
      debugPrint(
          '❌ QUIZ_SCREEN: Quiz is null or loading, showing loading indicator');
      return Scaffold(
        appBar: AppBar(title: const Text('Quiz')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'Generating your quiz...',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Text(
                'This may take a few moments',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    if (currentIndex >= quiz.questions.length) {
      debugPrint(
          '❌ QUIZ_SCREEN: Current index $currentIndex is out of bounds for quiz with ${quiz.questions.length} questions');
      // Handle the error gracefully by displaying an error message or redirecting
      return Scaffold(
        appBar: AppBar(title: const Text('Quiz Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('An error occurred with the quiz.',
                  style: TextStyle(fontSize: 18)),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/'),
                child: const Text('Return to Home'),
              ),
            ],
          ),
        ),
      );
    }

    final currentQuestion = quiz.questions[currentIndex];
    debugPrint(
        '🔍 QUIZ_SCREEN: Current question type: ${currentQuestion.runtimeType}');
    final progress = (currentIndex + 1) / quiz.questions.length;

    // Challenge mode - show timer prominently
    final isChallengeMode = quiz.mode == QuizMode.challenge;
    final challengePoints = ref.watch(challengePointsProvider);

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: AppBar(
          title: Text(isChallengeMode ? 'Challenge Quiz' : 'Vocabulary Quiz'),
          centerTitle: true,
          actions: [
            if (_isTimerRunning && isChallengeMode)
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Center(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: _remainingSeconds < 10
                          ? AppColors.incorrectLight
                          : Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${_remainingSeconds}s',
                      style: const TextStyle(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
        // Use a different key for each question to force a complete rebuild of the UI
        body: KeyedSubtree(
          key: ValueKey('question_$currentIndex'),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: Column(
              children: [
                // Progress indicator with skip button
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: progress,
                        minHeight: 8,
                        backgroundColor: AppColors.grey,
                        valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor),
                      ),
                    ),
                    const SizedBox(width: 12),
                    TextButton(
                      onPressed: _onQuestionSkipped,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: const Text(
                        'Skip',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Question counter
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Question ${currentIndex + 1}/${quiz.questions.length}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (isChallengeMode)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withAlpha(26),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          'Points: $challengePoints',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),

                // Timer for non-challenge mode (optional)
                if (_isTimerRunning && !isChallengeMode)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Text(
                      'Time remaining: $_remainingSeconds seconds',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _remainingSeconds < 10
                            ? AppColors.incorrectLight
                            : null,
                      ),
                    ),
                  ),

                // Actual question content
                Expanded(
                  child: _showingFeedback
                      ? _buildFeedbackContent()
                      : _buildQuestionWidget(currentQuestion),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionWidget(QuizQuestion question) {
    debugPrint(
        '🚨 QUIZ_SCREEN: Building widget for question type: ${question.runtimeType}');

    try {
      if (question is TrueFalseQuestion) {
        debugPrint('🚨 QUIZ_SCREEN: Using TrueFalseQuestionWidget');
        try {
          return TrueFalseQuestionWidget(
            question: question,
            onAnswerSelected: (answer) {
              debugPrint('🚨 QUIZ_SCREEN: TrueFalse answer selected: $answer');
              // TrueFalseQuestionWidget always returns a bool, so we just pass it directly
              _onAnswerSelected(answer);
            },
          );
        } catch (e) {
          debugPrint(
              '🚨 QUIZ_SCREEN: Error creating TrueFalseQuestionWidget: $e');
          // Fallback to a basic version if the widget fails
          return _buildFallbackTrueFalseQuestion(question);
        }
      } else if (question is FillInBlankQuestion) {
        debugPrint(
            '🚨 QUIZ_SCREEN: Using CardDragAndDropQuiz for fill-in-blank');

        // Create fallback in case the conversion fails
        try {
          final dragDropQuestion = CardDragAndDropQuizQuestion(
            questionText: question.sentence,
            options: question.options,
            correctAnswer: question.blankWord,
          );

          debugPrint('🚨 QUIZ_SCREEN: Fill-in-blank question details:');
          debugPrint('  - Question text: ${question.sentence}');
          debugPrint('  - Options: ${question.options}');
          debugPrint('  - Correct answer: ${question.blankWord}');

          return CardDragAndDropQuiz(
            question: dragDropQuestion,
            onCheck: _onAnswerSelected,
          );
        } catch (e) {
          debugPrint('🚨 QUIZ_SCREEN: Error creating CardDragAndDropQuiz: $e');
          // Fallback to a basic version if the widget fails
          return _buildFallbackFillInBlankQuestion(question);
        }
      } else if (question is SpellWordQuestion) {
        debugPrint('🚨 QUIZ_SCREEN: Using SpellWordQuestionWidget');
        try {
          return SpellWordQuestionWidget(
            question: question,
            onAnswerSelected: _onAnswerSelected,
          );
        } catch (e) {
          debugPrint(
              '🚨 QUIZ_SCREEN: Error creating SpellWordQuestionWidget: $e');
          // Fallback to a basic version if the widget fails
          return _buildBasicSpellWordQuestion(question);
        }
      } else if (question is MatchDefinitionQuestion) {
        debugPrint(
            '🚨 QUIZ_SCREEN: Using basic multiple choice for match definition');
        // Use a simpler implementation for match definition as fallback
        return _buildBasicMultipleChoiceQuestion(question);
      } else {
        debugPrint(
            '❌ QUIZ_SCREEN: Unsupported question type: ${question.runtimeType}');
        return _buildGenericFallbackQuestion(question);
      }
    } catch (e, stackTrace) {
      debugPrint('❌ QUIZ_SCREEN: Error building question widget: $e');
      debugPrint('❌ QUIZ_SCREEN: Stack trace: $stackTrace');
      return _buildGenericFallbackQuestion(question);
    }
  }

  // Basic fallback implementations for each question type to ensure something is shown
  Widget _buildFallbackTrueFalseQuestion(TrueFalseQuestion question) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'True or False?',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.grey,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey),
            ),
            child: Text(
              question.statement,
              style: TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 64),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () => _onAnswerSelected(false),
                child: Text('FALSE'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.incorrectLight,
                  foregroundColor: AppColors.white,
                  padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
              ),
              ElevatedButton(
                onPressed: () => _onAnswerSelected(true),
                child: Text('TRUE'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.green,
                  foregroundColor: AppColors.white,
                  padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFallbackFillInBlankQuestion(FillInBlankQuestion question) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Fill in the Blank',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.grey,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey),
            ),
            child: Text(
              question.sentence,
              style: TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 64),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            alignment: WrapAlignment.center,
            children: question.options.map((option) {
              return ElevatedButton(
                onPressed: () => _onAnswerSelected(option),
                child: Text(option),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicMultipleChoiceQuestion(MatchDefinitionQuestion question) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Match the Definition',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.infoLight,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.infoLight),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Definition:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.infoLight,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  question.definition,
                  style: TextStyle(fontSize: 18),
                ),
              ],
            ),
          ),
          SizedBox(height: 64),
          Column(
            children: question.options.map((option) {
              return Container(
                width: double.infinity,
                margin: EdgeInsets.only(bottom: 12),
                child: ElevatedButton(
                  onPressed: () => _onAnswerSelected(option),
                  child: Text(option, style: TextStyle(fontSize: 16)),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicSpellWordQuestion(SpellWordQuestion question) {
    debugPrint('🚨 QUIZ_SCREEN: Building basic spell word question fallback');

    // Create a list to track which letters have been used
    final letterUsed = List<bool>.filled(question.options.length, false);
    final spelledLetters = <String>[];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Spell the Word',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primaryLight,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primaryLight),
            ),
            child: Text(
              question.prompt,
              style: TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 32),

          // Answer area with blank spaces
          Container(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            decoration: BoxDecoration(
              color: AppColors.infoLight,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.infoLight,
                width: 1.5,
              ),
            ),
            child: Wrap(
              alignment: WrapAlignment.center,
              spacing: 8,
              runSpacing: 16,
              children: [
                for (int i = 0; i < question.correctWord.length; i++)
                  Container(
                    width: 36,
                    height: 36,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: AppColors.infoLight,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          SizedBox(height: 40),

          // Letter options in a grid
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 8,
            runSpacing: 12,
            children: List.generate(
              question.options.length,
              (index) => GestureDetector(
                onTap: () => _onAnswerSelected(question.correctWord),
                child: Container(
                  width: 55,
                  height: 55,
                  decoration: BoxDecoration(
                    color: AppColors.infoLight,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withAlpha(26),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      question.options[index],
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: AppColors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          SizedBox(height: 32),

          // Skip button at the bottom
          ElevatedButton(
            onPressed: () => _onAnswerSelected(question.correctWord),
            child: Text('Skip This Question'),
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenericFallbackQuestion(QuizQuestion question) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.quiz, color: AppColors.primaryLight, size: 64),
          const SizedBox(height: 16),
          Text(
            'Question about "${question.card.word}"',
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.grey,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.grey),
            ),
            child: Text(
              question.card.definition,
              style: const TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => _advanceToNextQuestion(),
            child: const Text('Continue'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackContent() {
    if (_lastAnsweredQuestion == null) {
      return const Center(child: Text('Error showing feedback'));
    }

    return QuizFeedbackScreen(
      question: _lastAnsweredQuestion!,
      userAnswer: _lastUserAnswer,
      userAnswerDefinition: _lastUserAnswerDefinition,
      isCorrect: _lastAnsweredQuestion!.isCorrect,
      onContinue: _onFeedbackContinue,
    );
  }

  void _onQuestionSkipped() {
    if (!_canAdvance || _showingFeedback) return;

    setState(() {
      _canAdvance = false;
    });

    final quiz = ref.read(currentQuizProvider);
    if (quiz == null) return;

    final questionIndex = ref.read(currentQuestionIndexProvider);

    debugPrint(
        '🔍 QUIZ_SCREEN: Question ${questionIndex + 1}/${quiz.questions.length} skipped');

    // Skip to next question without updating score or marking as answered
    _advanceToNextQuestion();
  }

  void _onAnswerSelected(dynamic answer, {String? answerDefinition}) {
    if (!_canAdvance || _showingFeedback) return;

    setState(() {
      _canAdvance = false;
    });

    final quiz = ref.read(currentQuizProvider);
    if (quiz == null) return;

    final questionIndex = ref.read(currentQuestionIndexProvider);
    final question = quiz.questions[questionIndex];

    debugPrint(
        '🔍 QUIZ_SCREEN: Answer selected for question ${questionIndex + 1}/${quiz.questions.length}');
    debugPrint('🔍 QUIZ_SCREEN: Answer: $answer');

    // Save the question and answer for feedback
    _lastAnsweredQuestion = question;
    _lastUserAnswer = answer is String ? answer : null;
    _lastUserAnswerDefinition = answerDefinition;

    // Use the provider to update the quiz state with the answer
    final answerQuestion = ref.read(answerQuestionProvider);
    final isCorrect = answerQuestion(answer);

    debugPrint(
        '🔍 QUIZ_SCREEN: Answer was ${isCorrect ? 'correct' : 'incorrect'}');

    // Play sound effect
    _playSound(isCorrect ? 'correct' : 'incorrect');

    // After a short delay, show feedback or go to next question
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // If this is the last question, complete the quiz
      final isLastQuestion = questionIndex == quiz.questions.length - 1;

      if (isLastQuestion) {
        debugPrint(
            '🔍 QUIZ_SCREEN: Last question completed, navigating to results');
        // If this is the last question, mark quiz as completed and go to results
        final updatedQuiz = ref.read(currentQuizProvider);
        if (updatedQuiz == null) return;

        // Cancel any running timer
        _timer?.cancel();
        _isTimerRunning = false;

        // Navigate to results
        if (mounted) {
          context.pushReplacement('/quiz/results', extra: updatedQuiz);
        }
        return;
      }

      // For challenge mode or if feedback is disabled, go directly to next question
      if (!quiz.showFeedback || quiz.mode == QuizMode.challenge) {
        debugPrint(
            '🔍 QUIZ_SCREEN: No feedback mode, advancing to next question');
        _advanceToNextQuestion();
      } else {
        // For train mode with feedback enabled, show the feedback screen
        debugPrint('🔍 QUIZ_SCREEN: Showing feedback before next question');
        setState(() {
          _showingFeedback = true;
        });
      }
    });
  }

  void _onFeedbackContinue() {
    debugPrint('🔍 QUIZ_SCREEN: User tapped continue after feedback');
    setState(() {
      _showingFeedback = false;
    });
    _advanceToNextQuestion();
  }

  void _advanceToNextQuestion() {
    final quiz = ref.read(currentQuizProvider);
    if (quiz == null) return;

    final currentIndex = ref.read(currentQuestionIndexProvider);
    final nextIndex = currentIndex + 1;

    debugPrint(
        '🔍 QUIZ_SCREEN: Advancing from question ${currentIndex + 1} to question ${nextIndex + 1}');

    // Check if we've reached the end of the quiz
    if (nextIndex >= quiz.questions.length) {
      debugPrint('🔍 QUIZ_SCREEN: Reached end of quiz, navigating to results');

      // Cancel any running timer
      _timer?.cancel();
      _isTimerRunning = false;

      // Navigate to results
      if (mounted) {
        context.pushReplacement('/quiz/results', extra: quiz);
      }
      return;
    }

    // Reset state for the next question
    setState(() {
      _showingFeedback = false;
      _canAdvance = true;
      _questionKey = UniqueKey();
    });

    // Move to the next question
    ref.read(currentQuestionIndexProvider.notifier).state = nextIndex;

    debugPrint('🔍 QUIZ_SCREEN: Question index updated to $nextIndex');
  }
}
