import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:toastification/toastification.dart';
import 'package:vocadex/firebase_options.dart';
import 'package:vocadex/src/app/app_services_launcher.dart';
import 'package:vocadex/src/common/widgets/gradient_scaffold.dart';
import 'package:vocadex/src/core/config/ad_config.dart';
import 'package:vocadex/src/core/config/api_constants.dart';
import 'package:vocadex/src/core/router/router_provider.dart';
import 'package:vocadex/src/core/theme/config/theme_config.dart';
import 'package:vocadex/src/core/theme/providers/theme_provider.dart';
import 'package:vocadex/src/features/ads/providers/admob_config_provider.dart';
import 'package:vocadex/src/features/ads/services/app_lifecycle_ad_manager.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/core/router/route_names.dart';
import 'package:vocadex/src/services/analytics_service.dart';
import 'package:vocadex/src/core/config/analytics_config.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

void main() async {
  //Git hy
  SentryWidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize Firebase App Check
    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.playIntegrity,
      appleProvider: AppleProvider.deviceCheck,
    );

    // Initialize RevenueCat
    // await _configureRevenueCat();
    await Purchases.configure(
      PurchasesConfiguration(revenueCatAppleApiKey),
    );

    // Initialize Mixpanel Analytics
    await AnalyticsService.instance
        .initialize(AnalyticsConfig.mixpanelProjectToken);
    await SentryFlutter.init(
      (options) {
        options.dsn =
            'https://<EMAIL>/4509591534895184';
        // Adds request headers and IP for users, for more info visit:
        // https://docs.sentry.io/platforms/dart/guides/flutter/data-management/data-collected/
        options.sendDefaultPii = true;
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        options.tracesSampleRate = 1.0;
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        options.profilesSampleRate = 1.0;
      },
      appRunner: () => runApp(SentryWidget(
        child: ProviderScope(
          child: AppServicesInitializer(
            child: const MainApp(),
          ),
        ),
      )),
    );
  } catch (e) {
    debugPrint('Error initializing app: $e');
    await SentryFlutter.init(
      (options) {
        options.dsn =
            'https://<EMAIL>/4509591534895184';
        // Adds request headers and IP for users, for more info visit:
        // https://docs.sentry.io/platforms/dart/guides/flutter/data-management/data-collected/
        options.sendDefaultPii = true;
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        options.tracesSampleRate = 1.0;
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        options.profilesSampleRate = 1.0;
      },
      appRunner: () => runApp(SentryWidget(
        child: const MaterialApp(
          home: GradientScaffold(
            body: Center(
              child: Text('Failed to initialize app. Please try again later.'),
            ),
          ),
        ),
      )),
    );
  }
}

class MainApp extends ConsumerStatefulWidget {
  const MainApp({super.key});

  @override
  ConsumerState<MainApp> createState() => _MainAppState();
}

class _MainAppState extends ConsumerState<MainApp> with WidgetsBindingObserver {
  DateTime? _sessionStartTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _sessionStartTime = DateTime.now();
    _trackAppLaunch();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // App came to foreground
        _sessionStartTime = DateTime.now();
        _trackAppResumed();
        break;
      case AppLifecycleState.paused:
        // App went to background
        _trackAppPaused();
        break;
      case AppLifecycleState.detached:
        // App is being terminated
        _trackSessionEnd();
        break;
      default:
        break;
    }
  }

  Future<void> _trackAppLaunch() async {
    try {
      await AnalyticsService.instance.track('App Launched', {
        'Launch Time': DateTime.now().toIso8601String(),
        'Day of Week': DateTime.now().weekday,
        'Hour of Day': DateTime.now().hour,
      });
    } catch (e) {
      debugPrint('Analytics tracking error: $e');
    }
  }

  Future<void> _trackAppResumed() async {
    try {
      await AnalyticsService.instance.track('App Resumed', {
        'Resume Time': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('Analytics tracking error: $e');
    }
  }

  Future<void> _trackAppPaused() async {
    try {
      if (_sessionStartTime != null) {
        final sessionDuration = DateTime.now().difference(_sessionStartTime!);
        await AnalyticsService.instance.track('App Paused', {
          'Session Duration (minutes)': sessionDuration.inMinutes,
          'Session Duration (seconds)': sessionDuration.inSeconds,
        });
      }
    } catch (e) {
      debugPrint('Analytics tracking error: $e');
    }
  }

  Future<void> _trackSessionEnd() async {
    try {
      await AnalyticsService.instance.trackSessionEnd();
    } catch (e) {
      debugPrint('Analytics tracking error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    final goRouter = ref.read(routerProvider);

    // Initialize AdConfig cache
    ref.listen(admobConfigProvider, (previous, next) {
      next.whenData((config) {
        AdConfig.updateCache(config);
      });
    });

    // Initialize app lifecycle ad manager
    ref.watch(appLifecycleAdManagerProvider);

    // Listen to auth state changes for global navigation
    ref.listen(authStateNotifierProvider, (previous, next) {
      next.whenOrNull(
        unauthenticated: () {
          // When user becomes unauthenticated (signs out or account deleted),
          // navigate to onboarding screen starting at welcome
          goRouter.goNamed(RouteNames.onboarding);
        },
      );
    });

    return ToastificationWrapper(
      child: MaterialApp.router(
        debugShowCheckedModeBanner: false,
        routerConfig: goRouter,
        themeMode: themeMode.when(
          data: (mode) => mode,
          loading: () => ThemeMode.system,
          error: (_, __) => ThemeMode.system,
        ),
        theme: ThemeConfig.lightTheme,
        darkTheme: ThemeConfig.darkTheme,
      ),
    );
  }
}
