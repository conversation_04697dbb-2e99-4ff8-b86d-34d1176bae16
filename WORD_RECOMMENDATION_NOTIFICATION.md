# Word Recommendation Notification Feature

## Overview

The Word Recommendation notification feature encourages users to practice vocabulary words with low mastery levels from their personal deck. This feature identifies words that need attention and sends daily notifications to help users improve their vocabulary mastery.

## How It Works

### Mastery Level System
- **Wild (Levels 1-3)**: Words that need the most practice
- **Tamed (Levels 4-6)**: Words with moderate mastery
- **Mastered (Levels 7-10)**: Well-learned words

### Recommendation Logic
The system prioritizes words for recommendation based on:
1. **Mastery Level**: Lower mastery levels get higher priority
2. **Last Reviewed Date**: Older or never-reviewed words get priority
3. **Random Selection**: From top candidates to add variety

## Implementation Details

### New Trigger Type
- **Type**: `TriggerType.wordRecommendation`
- **Schedule**: Daily at 10:00 AM
- **Icon**: Lightbulb (💡)
- **Conditions**:
  - Requires at least 3 words in deck
  - Requires words with low mastery levels (1-3)
  - Skips if no low mastery words exist

### Files Modified/Added

#### 1. Trigger Event Model
- **File**: `lib/src/features/notifications/models/trigger_event_model.dart`
- **Changes**: Added `wordRecommendation` to `TriggerType` enum

#### 2. Trigger Event Factory
- **File**: `lib/src/features/notifications/utils/trigger_event_factory.dart`
- **Changes**: 
  - Added `createWordRecommendationNotification()` method
  - Updated all switch cases to handle new trigger type
  - Added to default triggers list

#### 3. Trigger Management UI
- **File**: `lib/src/features/notifications/presentation/widgets/trigger_management_card.dart`
- **Changes**: Added lightbulb icon for word recommendation trigger

#### 4. Word Recommendation Service (NEW)
- **File**: `lib/src/features/notifications/services/word_recommendation_service.dart`
- **Features**:
  - Get single word recommendation
  - Get multiple word recommendations
  - Check if user has words needing practice
  - Get mastery statistics
  - Generate personalized recommendation messages

## Usage Examples

### Getting a Word Recommendation
```dart
// Using the provider
final wordRecommendation = ref.watch(wordRecommendationProvider);

wordRecommendation.when(
  data: (word) {
    if (word != null) {
      // Show recommended word to user
      print('Practice this word: ${word.word}');
    } else {
      // No words need practice
      print('Great! No words need practice right now.');
    }
  },
  loading: () => CircularProgressIndicator(),
  error: (error, stack) => Text('Error: $error'),
);
```

### Getting Multiple Recommendations
```dart
final recommendations = ref.watch(multipleWordRecommendationsProvider(count: 5));

recommendations.when(
  data: (words) {
    // Display list of recommended words
    for (final word in words) {
      print('${word.word} - Mastery: ${word.masteryLevel}');
    }
  },
  loading: () => CircularProgressIndicator(),
  error: (error, stack) => Text('Error: $error'),
);
```

### Checking if Practice is Needed
```dart
final needsPractice = ref.watch(hasWordsNeedingPracticeProvider);

needsPractice.when(
  data: (needs) {
    if (needs) {
      // Show practice encouragement
    } else {
      // Show congratulations
    }
  },
  loading: () => CircularProgressIndicator(),
  error: (error, stack) => Text('Error: $error'),
);
```

### Getting Mastery Statistics
```dart
final stats = ref.watch(masteryStatisticsProvider);

stats.when(
  data: (statistics) {
    print('Wild words: ${statistics['wild']}');
    print('Tamed words: ${statistics['tamed']}');
    print('Mastered words: ${statistics['mastered']}');
    print('Total words: ${statistics['total']}');
  },
  loading: () => CircularProgressIndicator(),
  error: (error, stack) => Text('Error: $error'),
);
```

## Notification Configuration

### Default Settings
- **Enabled**: True by default
- **Schedule**: Daily at 10:00 AM
- **Title**: "Practice Time! 📚"
- **Message**: "Ready to improve your vocabulary? Practice a word from your deck that needs attention!"

### Conditions
- **requiresLowMasteryWords**: Only send if user has words with mastery levels 1-3
- **minimumWordsInDeck**: Requires at least 3 words in user's deck
- **skipIfNoLowMasteryWords**: Skip notification if no low mastery words exist

## Admin Configuration

The notification can be managed through the admin panel:
1. Enable/disable the word recommendation trigger
2. Modify the schedule (time of day)
3. Customize the notification message
4. Adjust conditions (minimum words, mastery thresholds)

## Future Enhancements

Potential improvements for this feature:
1. **Dynamic Content**: Include the actual recommended word in the notification
2. **Smart Timing**: Send notifications when user is most likely to practice
3. **Streak Integration**: Combine with streak reminders for better engagement
4. **Personalized Messages**: Customize messages based on user progress
5. **Difficulty Progression**: Gradually introduce harder words as mastery improves

## Testing

To test the word recommendation feature:
1. Add vocabulary words to your deck
2. Ensure some words have low mastery levels (1-3)
3. Enable the word recommendation notification in admin panel
4. Check that notifications are scheduled correctly
5. Verify that recommendations prioritize low mastery words
