// test/quiz_content_generation_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:vocadex/src/features/vocab_card/model_vocabcard.dart';
import 'package:vocadex/src/features/vocab_quiz/models/quiz_content_models.dart';

void main() {
  group('Quiz Content Models Tests', () {
    test('WordQuizContent model serialization works correctly', () {
      // Create test data
      final fillInBlankContent = FillInBlankContent(
        id: 'test-fill-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        sentence: 'The cat sat on the _____',
        correctAnswer: 'mat',
        distractors: ['hat', 'bat', 'rat'],
        context: 'casual',
      );

      final trueFalseContent = TrueFalseContent(
        id: 'test-tf-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        statement: 'A mat is used for sleeping',
        isTrue: false,
        explanation: 'A mat is typically used for floor covering, not sleeping',
        category: 'definition',
      );

      final spellWordContent = SpellWordContent(
        id: 'test-spell-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        prompt: 'A flat piece of material used on floors',
        answer: 'mat',
        promptType: 'definition',
      );

      final wordQuizContent = WordQuizContent(
        wordId: 'test-word-id',
        word: 'mat',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        contentVersion: 1,
        fillInBlankQuestions: [fillInBlankContent],
        trueFalseQuestions: [trueFalseContent],
        spellWordQuestions: [spellWordContent],
      );

      // Test serialization
      final firestoreData = wordQuizContent.toFirestore();
      expect(firestoreData, isA<Map<String, dynamic>>());
      expect(firestoreData['metadata']['word'], equals('mat'));
      expect(firestoreData['fillInBlank'], isA<List>());
      expect(firestoreData['trueFalse'], isA<List>());
      expect(firestoreData['spellWord'], isA<List>());

      // Test deserialization
      final reconstructed =
          WordQuizContent.fromFirestore('test-word-id', firestoreData);
      expect(reconstructed.word, equals('mat'));
      expect(reconstructed.fillInBlankQuestions.length, equals(1));
      expect(reconstructed.trueFalseQuestions.length, equals(1));
      expect(reconstructed.spellWordQuestions.length, equals(1));
      expect(reconstructed.totalQuestions, equals(3));
    });

    test('Quiz content models have correct properties', () {
      final fillInBlank = FillInBlankContent(
        id: 'test-1',
        difficulty: 'A1',
        createdAt: DateTime.now(),
        sentence: 'I love to _____ books',
        correctAnswer: 'read',
        distractors: ['write', 'buy', 'sell'],
        context: 'educational',
      );

      expect(fillInBlank.sentence, contains('_____'));
      expect(fillInBlank.correctAnswer, equals('read'));
      expect(fillInBlank.distractors.length, equals(3));
      expect(fillInBlank.context, equals('educational'));

      final trueFalse = TrueFalseContent(
        id: 'test-2',
        difficulty: 'A2',
        createdAt: DateTime.now(),
        statement: 'Reading helps improve vocabulary skills',
        isTrue: true,
        explanation: 'Reading exposes people to new words and contexts',
        category: 'characteristics',
      );

      expect(trueFalse.statement, isNotEmpty);
      expect(trueFalse.isTrue, isTrue);
      expect(trueFalse.explanation, isNotEmpty);
      expect(trueFalse.category, equals('characteristics'));

      final spellWord = SpellWordContent(
        id: 'test-3',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        prompt: 'What you do when looking at written text to understand it',
        answer: 'read',
        promptType: 'usage',
      );

      expect(spellWord.prompt, isNotEmpty);
      expect(spellWord.answer, equals('read'));
      expect(spellWord.promptType, equals('usage'));
    });

    test('VocabCard.createGlobalId generates consistent IDs', () {
      // Test that the same word always generates the same ID
      final id1 = VocabCard.createGlobalId('example');
      final id2 = VocabCard.createGlobalId('example');
      final id3 = VocabCard.createGlobalId('EXAMPLE');
      final id4 = VocabCard.createGlobalId('Example');

      expect(id1, equals(id2));
      expect(id1, equals(id3));
      expect(id1, equals(id4));

      // Test that different words generate different IDs
      final differentId = VocabCard.createGlobalId('different');
      expect(id1, isNot(equals(differentId)));
    });

    test('WordQuizContent provides correct statistics', () {
      final content = WordQuizContent(
        wordId: 'test-id',
        word: 'test',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        contentVersion: 1,
        fillInBlankQuestions: [
          FillInBlankContent(
            id: '1',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            sentence: 'Test sentence 1 _____',
            correctAnswer: 'test',
            distractors: ['a', 'b', 'c'],
            context: 'test',
          ),
          FillInBlankContent(
            id: '2',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            sentence: 'Test sentence 2 _____',
            correctAnswer: 'test',
            distractors: ['d', 'e', 'f'],
            context: 'test',
          ),
        ],
        trueFalseQuestions: [
          TrueFalseContent(
            id: '3',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            statement: 'Test statement',
            isTrue: true,
            explanation: 'Test explanation',
            category: 'test',
          ),
        ],
        spellWordQuestions: [
          SpellWordContent(
            id: '4',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            prompt: 'Test prompt',
            answer: 'test',
            promptType: 'test',
          ),
        ],
      );

      expect(content.totalQuestions, equals(4));
      expect(content.hasContent, isTrue);
      expect(content.fillInBlankQuestions.length, equals(2));
      expect(content.trueFalseQuestions.length, equals(1));
      expect(content.spellWordQuestions.length, equals(1));
    });

    test('Empty WordQuizContent reports correctly', () {
      final emptyContent = WordQuizContent(
        wordId: 'empty-id',
        word: 'empty',
        difficulty: 'A1',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        contentVersion: 1,
        fillInBlankQuestions: [],
        trueFalseQuestions: [],
        spellWordQuestions: [],
      );

      expect(emptyContent.totalQuestions, equals(0));
      expect(emptyContent.hasContent, isFalse);
    });
  });

  group('Quiz Content Uniqueness Tests', () {
    test('Fill-in-blank and spell-word questions have different sentences', () {
      // Create test content with different sentences for different question types
      final fillInBlankContent = FillInBlankContent(
        id: 'test-fill-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        sentence: 'I have complete _____ trust in my doctor',
        correctAnswer: 'faith',
        distractors: ['doubt', 'fear', 'worry'],
        context: 'medical',
      );

      final spellWordContent = SpellWordContent(
        id: 'test-spell-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        prompt: 'Strong belief or confidence in someone or something',
        answer: 'faith',
        promptType: 'definition',
      );

      // Verify that the sentences/prompts are different
      expect(
          fillInBlankContent.sentence, isNot(equals(spellWordContent.prompt)));

      // Verify that fill-in-blank uses a sentence with blank
      expect(fillInBlankContent.sentence, contains('_____'));

      // Verify that spell-word uses a descriptive prompt without blanks
      expect(spellWordContent.prompt, isNot(contains('_____')));
      expect(spellWordContent.prompt, isNot(contains('blank')));

      // Both should target the same word but with different approaches
      expect(fillInBlankContent.correctAnswer, equals(spellWordContent.answer));
    });

    test('Multiple fill-in-blank questions have unique sentences', () {
      final fillInBlank1 = FillInBlankContent(
        id: 'test-fill-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        sentence: 'She showed great _____ during the difficult times',
        correctAnswer: 'courage',
        distractors: ['fear', 'doubt', 'worry'],
        context: 'emotional',
      );

      final fillInBlank2 = FillInBlankContent(
        id: 'test-fill-2',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        sentence: 'The soldier displayed remarkable _____ in battle',
        correctAnswer: 'courage',
        distractors: ['weakness', 'hesitation', 'retreat'],
        context: 'military',
      );

      // Verify sentences are different even for the same word
      expect(fillInBlank1.sentence, isNot(equals(fillInBlank2.sentence)));

      // Both should have the same answer but different contexts
      expect(fillInBlank1.correctAnswer, equals(fillInBlank2.correctAnswer));
      expect(fillInBlank1.context, isNot(equals(fillInBlank2.context)));
    });

    test('True/false statements are unique and contextually different', () {
      final trueFalse1 = TrueFalseContent(
        id: 'test-tf-1',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        statement: 'Courage means being afraid but acting anyway',
        isTrue: true,
        explanation:
            'Courage is not the absence of fear, but action in spite of fear',
        category: 'definition',
      );

      final trueFalse2 = TrueFalseContent(
        id: 'test-tf-2',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        statement: 'Only soldiers can show courage',
        isTrue: false,
        explanation: 'Courage can be shown by anyone in various situations',
        category: 'characteristics',
      );

      // Verify statements are different
      expect(trueFalse1.statement, isNot(equals(trueFalse2.statement)));

      // Verify they have different truth values and categories
      expect(trueFalse1.isTrue, isNot(equals(trueFalse2.isTrue)));
      expect(trueFalse1.category, isNot(equals(trueFalse2.category)));
    });

    test('WordQuizContent ensures no sentence repetition across question types',
        () {
      final wordQuizContent = WordQuizContent(
        wordId: 'test-word-courage',
        word: 'courage',
        difficulty: 'B1',
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        contentVersion: 1,
        fillInBlankQuestions: [
          FillInBlankContent(
            id: 'fill-1',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            sentence: 'She showed great _____ during the crisis',
            correctAnswer: 'courage',
            distractors: ['fear', 'doubt', 'panic'],
            context: 'crisis',
          ),
          FillInBlankContent(
            id: 'fill-2',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            sentence: 'It takes _____ to stand up for what is right',
            correctAnswer: 'courage',
            distractors: ['weakness', 'cowardice', 'hesitation'],
            context: 'moral',
          ),
        ],
        trueFalseQuestions: [
          TrueFalseContent(
            id: 'tf-1',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            statement: 'Courage is the opposite of fear',
            isTrue: false,
            explanation: 'Courage exists alongside fear, not in its absence',
            category: 'definition',
          ),
        ],
        spellWordQuestions: [
          SpellWordContent(
            id: 'spell-1',
            difficulty: 'B1',
            createdAt: DateTime.now(),
            prompt:
                'The quality of mind that enables one to face danger with confidence',
            answer: 'courage',
            promptType: 'definition',
          ),
        ],
      );

      // Collect all sentences/prompts/statements
      final allTexts = <String>[];

      // Add fill-in-blank sentences
      for (final question in wordQuizContent.fillInBlankQuestions) {
        allTexts.add(question.sentence);
      }

      // Add true/false statements
      for (final question in wordQuizContent.trueFalseQuestions) {
        allTexts.add(question.statement);
      }

      // Add spell word prompts
      for (final question in wordQuizContent.spellWordQuestions) {
        allTexts.add(question.prompt);
      }

      // Verify all texts are unique
      final uniqueTexts = allTexts.toSet();
      expect(uniqueTexts.length, equals(allTexts.length),
          reason:
              'All question texts should be unique. Found duplicates in: $allTexts');

      // Verify that no fill-in-blank sentence is used as a spell word prompt
      for (final fillInBlank in wordQuizContent.fillInBlankQuestions) {
        for (final spellWord in wordQuizContent.spellWordQuestions) {
          expect(fillInBlank.sentence, isNot(equals(spellWord.prompt)),
              reason:
                  'Fill-in-blank sentences should not be reused as spell word prompts');
        }
      }
    });
  });
}
